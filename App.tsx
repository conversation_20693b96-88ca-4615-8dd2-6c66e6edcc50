import React, { useState } from 'react';
import { StyleSheet, Text, View, TextInput, TouchableOpacity, Alert, ActivityIndicator, ScrollView, SafeAreaView } from 'react-native';
import { StatusBar } from 'expo-status-bar';

export default function App() {
  const [searchWord, setSearchWord] = useState('');
  const [currentDefinition, setCurrentDefinition] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const searchDefinition = async () => {
    if (!searchWord.trim()) {
      Alert.alert('Error', 'Please enter a word to search');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`https://api.dictionaryapi.dev/api/v2/entries/en/${searchWord.trim()}`);

      if (response.ok) {
        const data = await response.json();
        const definitions: string[] = [];

        for (const meaning of data[0]['meanings']) {
          for (const definition of meaning['definitions']) {
            definitions.push(`• ${definition['definition']}`);
          }
        }

        const definitionText = definitions.join('\n\n');
        setCurrentDefinition(definitionText);

      } else {
        setCurrentDefinition('No definition found for this word.');
      }
    } catch (error) {
      console.error('Error fetching definition:', error);
      setCurrentDefinition('Error fetching definition. Please check your internet connection.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>📚 Dictionary App</Text>
        <Text style={styles.subtitle}>Powered by AI Agent</Text>
      </View>

      {/* Search Section */}
      <View style={styles.searchSection}>
        <TextInput
          style={styles.searchInput}
          placeholder="Enter a word to search..."
          value={searchWord}
          onChangeText={setSearchWord}
          onSubmitEditing={searchDefinition}
          autoCapitalize="none"
          autoCorrect={false}
        />
        <TouchableOpacity
          style={styles.searchButton}
          onPress={searchDefinition}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <Text style={styles.searchButtonText}>Search</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Current Definition */}
        {currentDefinition ? (
          <View style={styles.definitionSection}>
            <Text style={styles.definitionTitle}>Definition:</Text>
            <Text style={styles.definitionText}>{currentDefinition}</Text>
          </View>
        ) : (
          <View style={styles.welcomeSection}>
            <Text style={styles.welcomeText}>
              Welcome to Dictionary App! 🎉{'\n\n'}
              Enter any word above to get its definition using our AI-powered dictionary agent.
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#4a90e2',
    paddingVertical: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 14,
    color: '#e3f2fd',
    fontStyle: 'italic',
  },
  searchSection: {
    flexDirection: 'row',
    padding: 20,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchInput: {
    flex: 1,
    height: 50,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 25,
    paddingHorizontal: 20,
    fontSize: 16,
    backgroundColor: '#f8f9fa',
    marginRight: 10,
  },
  searchButton: {
    backgroundColor: '#4a90e2',
    paddingHorizontal: 25,
    paddingVertical: 15,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 80,
  },
  searchButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  welcomeSection: {
    backgroundColor: '#fff',
    padding: 30,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  welcomeText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  definitionSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  definitionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  definitionText: {
    fontSize: 16,
    color: '#555',
    lineHeight: 24,
  },
  historySection: {
    marginTop: 10,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  clearButton: {
    color: '#e74c3c',
    fontSize: 14,
    fontWeight: '600',
  },
  historyItem: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  historyWord: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4a90e2',
    marginBottom: 5,
  },
  historyDefinition: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});
