const e=Object.freeze(JSON.parse(`{"displayName":"dotEnv","name":"dotenv","patterns":[{"captures":{"1":{"patterns":[{"include":"#line-comment"}]}},"comment":"Full Line Comment","match":"^\\\\s?(#.*$)\\\\n"},{"captures":{"1":{"patterns":[{"include":"#key"}]},"2":{"name":"keyword.operator.assignment.dotenv"},"3":{"name":"property.value.dotenv","patterns":[{"include":"#line-comment"},{"include":"#double-quoted-string"},{"include":"#single-quoted-string"},{"include":"#interpolation"}]}},"comment":"ENV entry","match":"^\\\\s?(.*?)\\\\s?(\\\\=)(.*)$"}],"repository":{"double-quoted-string":{"captures":{"1":{"patterns":[{"include":"#interpolation"},{"include":"#escape-characters"}]}},"comment":"Double Quoted String","match":"\\"(.*)\\"","name":"string.quoted.double.dotenv"},"escape-characters":{"comment":"Escape characters","match":"\\\\\\\\[nrtfb\\"'\\\\\\\\]|\\\\\\\\u[0123456789ABCDEF]{4}","name":"constant.character.escape.dotenv"},"interpolation":{"captures":{"1":{"name":"keyword.interpolation.begin.dotenv"},"2":{"name":"variable.interpolation.dotenv"},"3":{"name":"keyword.interpolation.end.dotenv"}},"comment":"Interpolation (variable substitution)","match":"(\\\\$\\\\{)(.*)(\\\\})"},"key":{"captures":{"1":{"name":"keyword.key.export.dotenv"},"2":{"name":"variable.key.dotenv","patterns":[{"include":"#variable"}]}},"comment":"Key","match":"(export\\\\s)?(.*)"},"line-comment":{"comment":"Comment","match":"#.*$","name":"comment.line.dotenv"},"single-quoted-string":{"comment":"Single Quoted String","match":"'(.*)'","name":"string.quoted.single.dotenv"},"variable":{"comment":"env variable","match":"[a-zA-Z_]+[a-zA-Z0-9_]*"}},"scopeName":"source.dotenv"}`)),t=[e];export{t as default};
